"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_user = require("../../src/api/modules/user.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_checkbox2 = common_vendor.resolveComponent("u-checkbox");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  const _easycom_u_toast2 = common_vendor.resolveComponent("u-toast");
  (_easycom_u_icon2 + _easycom_u_button2 + _easycom_u_checkbox2 + _easycom_u_modal2 + _easycom_u_toast2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_checkbox = () => "../../uni_modules/uview-plus/components/u-checkbox/u-checkbox.js";
const _easycom_u_modal = () => "../../uni_modules/uview-plus/components/u-modal/u-modal.js";
const _easycom_u_toast = () => "../../uni_modules/uview-plus/components/u-toast/u-toast.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_button + _easycom_u_checkbox + _easycom_u_modal + _easycom_u_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const { setProfile } = userStore;
    const agreedToTerms = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const showUserAgreementModal = common_vendor.ref(false);
    const showPrivacyPolicyModal = common_vendor.ref(false);
    const toastRef = common_vendor.ref(null);
    const loginButtonStyle = common_vendor.computed(() => ({
      width: "100%",
      height: "96rpx",
      background: agreedToTerms.value ? "linear-gradient(135deg, #4CAF50, #2196F3)" : "#e0e0e0",
      color: "#ffffff",
      border: "none",
      borderRadius: "48rpx",
      boxShadow: agreedToTerms.value ? "0 8rpx 24rpx rgba(76, 175, 80, 0.3), 0 4rpx 12rpx rgba(33, 150, 243, 0.2)" : "0 2rpx 8rpx rgba(0, 0, 0, 0.1)",
      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      opacity: agreedToTerms.value ? 1 : 0.6,
      transform: agreedToTerms.value ? "translateY(0)" : "translateY(2rpx)"
    }));
    const userAgreementContent = common_vendor.computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`);
    const privacyPolicyContent = common_vendor.computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`);
    function showUserAgreement() {
      showUserAgreementModal.value = true;
    }
    function showPrivacyPolicy() {
      showPrivacyPolicyModal.value = true;
    }
    function showToast(title, type = "info") {
      if (toastRef.value) {
        toastRef.value.show({
          title,
          type,
          duration: type === "success" ? 1500 : 2e3
        });
      }
    }
    async function handleWxLogin() {
      if (!agreedToTerms.value) {
        showToast("请先同意用户协议", "warning");
        return;
      }
      isLoading.value = true;
      try {
        const loginResult = await new Promise((resolve, reject) => {
          common_vendor.index.login({
            provider: "weixin",
            success: resolve,
            fail: reject
          });
        });
        const loginParams = {
          code: loginResult.code
        };
        const userInfo = await src_api_modules_user.wxLogin(loginParams);
        setProfile(userInfo);
        showToast("登录成功", "success");
        setTimeout(() => {
          navigateByUserStatus(userInfo.status);
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:257", "微信登录失败:", error);
        showToast("登录失败，请重试", "error");
      } finally {
        isLoading.value = false;
      }
    }
    function navigateByUserStatus(status) {
      switch (status) {
        case "approved":
          common_vendor.index.reLaunch({ url: "/pages/info/info" });
          break;
        case "pending":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "rejected":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "incomplete":
        default:
          common_vendor.index.navigateTo({ url: "/pages/register/register" });
          break;
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "medical-bag",
          size: "48",
          color: "#ffffff"
        }),
        b: common_vendor.o(handleWxLogin),
        c: common_vendor.p({
          type: "primary",
          text: "微信授权登录",
          icon: "weixin-fill",
          disabled: !agreedToTerms.value || isLoading.value,
          loading: isLoading.value,
          loadingText: "登录中...",
          customStyle: loginButtonStyle.value,
          shape: "circle",
          size: "large",
          throttleTime: 1e3
        }),
        d: common_vendor.o(($event) => agreedToTerms.value = $event),
        e: common_vendor.p({
          activeColor: "#4CAF50",
          inactiveColor: "#e0e0e0",
          iconColor: "#ffffff",
          size: "20",
          iconSize: "14",
          shape: "circle",
          disabled: isLoading.value,
          usedAlone: true,
          label: "我已阅读并同意",
          checked: agreedToTerms.value
        }),
        f: common_vendor.o(showUserAgreement),
        g: common_vendor.o(showPrivacyPolicy),
        h: common_vendor.t(userAgreementContent.value),
        i: common_vendor.o(($event) => showUserAgreementModal.value = false),
        j: common_vendor.o(($event) => showUserAgreementModal.value = $event),
        k: common_vendor.p({
          title: "用户服务协议",
          showCancelButton: false,
          confirmText: "我知道了",
          confirmColor: "#4CAF50",
          modelValue: showUserAgreementModal.value
        }),
        l: common_vendor.t(privacyPolicyContent.value),
        m: common_vendor.o(($event) => showPrivacyPolicyModal.value = false),
        n: common_vendor.o(($event) => showPrivacyPolicyModal.value = $event),
        o: common_vendor.p({
          title: "隐私政策",
          showCancelButton: false,
          confirmText: "我知道了",
          confirmColor: "#4CAF50",
          modelValue: showPrivacyPolicyModal.value
        }),
        p: common_vendor.sr(toastRef, "e4e4508d-5", {
          "k": "toastRef"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
