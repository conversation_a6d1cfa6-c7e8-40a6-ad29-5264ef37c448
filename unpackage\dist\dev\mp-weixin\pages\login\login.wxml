<view class="login-page data-v-e4e4508d"><view class="background-decoration data-v-e4e4508d"><view class="decoration-circle decoration-circle--1 data-v-e4e4508d"></view><view class="decoration-circle decoration-circle--2 data-v-e4e4508d"></view><view class="decoration-circle decoration-circle--3 data-v-e4e4508d"></view></view><view class="main-container data-v-e4e4508d"><view class="brand-section data-v-e4e4508d"><view class="logo-wrapper data-v-e4e4508d"><view class="logo-background data-v-e4e4508d"><u-icon wx:if="{{a}}" class="data-v-e4e4508d" u-i="e4e4508d-0" bind:__l="__l" u-p="{{a}}"/></view></view><view class="brand-text data-v-e4e4508d"><text class="main-title data-v-e4e4508d">疾控考试系统</text><text class="sub-title data-v-e4e4508d">医护任职资格考试平台</text></view></view><view class="login-section data-v-e4e4508d"><view class="login-button-wrapper data-v-e4e4508d"><u-button wx:if="{{c}}" class="data-v-e4e4508d" bindclick="{{b}}" u-i="e4e4508d-1" bind:__l="__l" u-p="{{c}}"/></view><view class="agreement-section data-v-e4e4508d"><view class="checkbox-container data-v-e4e4508d"><u-checkbox wx:if="{{e}}" class="data-v-e4e4508d" u-i="e4e4508d-2" bind:__l="__l" bindupdateChecked="{{d}}" u-p="{{e}}"/></view><view class="agreement-links data-v-e4e4508d"><text class="agreement-link data-v-e4e4508d" bindtap="{{f}}">《用户服务协议》</text><text class="agreement-text data-v-e4e4508d">和</text><text class="agreement-link data-v-e4e4508d" bindtap="{{g}}">《隐私政策》</text></view></view></view></view><u-modal wx:if="{{k}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindconfirm="{{i}}" u-i="e4e4508d-3" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"><view class="modal-content data-v-e4e4508d"><text class="modal-text data-v-e4e4508d">{{h}}</text></view></u-modal><u-modal wx:if="{{o}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindconfirm="{{m}}" u-i="e4e4508d-4" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"><view class="modal-content data-v-e4e4508d"><text class="modal-text data-v-e4e4508d">{{l}}</text></view></u-modal><u-toast class="r data-v-e4e4508d" u-r="toastRef" u-i="e4e4508d-5" bind:__l="__l"/></view>